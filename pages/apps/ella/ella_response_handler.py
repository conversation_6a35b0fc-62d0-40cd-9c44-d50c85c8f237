"""
Ella响应处理器
负责处理AI响应的获取、验证和分析
"""
import time
import re
from core.logger import log


class EllaResponseHandler:
    """Ella响应处理器"""

    def __init__(self, driver=None, status_checker=None):
        """
        初始化响应处理器

        Args:
            driver: UIAutomator2驱动实例
            status_checker: Ella状态检查器实例
        """
        self.driver = driver
        self.status_checker = status_checker

    def wait_for_response(self, timeout: int = 10) -> bool:
        """
        等待AI响应出现
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否检测到响应
        """
        try:
            log.info(f"等待AI响应，超时时间: {timeout}秒")

            if not self.driver:
                log.error("驱动实例未初始化")
                return False

            # 获取初始页面状态
            initial_count = self._get_quick_element_count()
            initial_snapshot = self._get_page_text_snapshot()

            start_time = time.time()

            while time.time() - start_time < timeout:
                # 方法1: 检查元素数量变化
                if self._quick_check_for_response(initial_count):
                    log.info("✅ 通过元素数量变化检测到响应")
                    return True

                # 方法2: 检查页面文本变化
                if self._check_for_new_response(initial_snapshot):
                    log.info("✅ 通过页面文本变化检测到响应")
                    return True

                # 方法3: 检查TTS按钮出现
                if self._check_tts_button_appeared():
                    log.info("✅ 通过TTS按钮检测到响应")
                    return True

                time.sleep(0.5)

            log.warning(f"⚠️ 等待响应超时 ({timeout}秒)")
            return False

        except Exception as e:
            log.error(f"等待响应失败: {e}")
            return False

    def get_response_text(self) -> str:
        """
        获取AI响应文本
        优先从指定的check_area节点获取所有文本信息

        Returns:
            str: 响应文本内容
        """
        try:
            if not self.driver:
                log.error("驱动实例未初始化")
                return ""

            # 确保在Ella页面
            if not self._ensure_on_ella_page():
                log.error("无法确保在Ella页面，无法获取响应文本")
                return ""

            log.info("获取AI响应文本")

            # 优先方法：从指定的check_area节点获取文本
            response = self._get_response_from_check_area()
            if response and self._is_meaningful_text(response):
                log.info(f"✅ 从check_area获取到响应文本: {response}")
                return response

            # 备用方法：尝试其他方法获取响应文本
            response_methods = [
                self._get_response_from_text_views,
                self._get_response_from_chat_list,
                self._get_response_from_page_dump
            ]

            for method in response_methods:
                try:
                    response = method()
                    if response and self._is_meaningful_text(response):
                        log.info(f"✅ 获取到响应文本: {response}")
                        return response
                except Exception as e:
                    log.debug(f"响应获取方法失败: {e}")
                    continue

            log.warning("未获取到有效的响应文本")
            return ""

        except Exception as e:
            log.error(f"获取响应文本失败: {e}")
            return ""

    def get_response_all_text(self) -> list:
        """
        获取AI响应文本
        优先从指定的check_area节点获取所有文本信息

        Returns:
            str: 响应文本内容
        """
        try:
            if not self.driver:
                log.error("驱动实例未初始化")
                return []

            # 确保在Ella页面
            if not self._ensure_on_ella_page():
                log.error("无法确保在Ella页面，无法获取响应文本")
                return []

            log.info("获取AI响应文本")
            all_text = []
            # 优先方法：从指定的节点获取文本
            asr_txt = self.get_response_from_asr_txt()
            robot_text = self.get_response_from_robot_text()
            function_name = self.get_response_from_function_name()
            function_control = self.get_response_from_function_control()
            tv_card_chat_gpt = self.get_response_from_tv_card_chat_gpt()
            tv_top = self.get_response_from_tv_top()

            all_text.append(asr_txt)
            all_text.append(robot_text)
            all_text.append(function_name)
            all_text.append(function_control)
            all_text.append(tv_card_chat_gpt)
            all_text.append(tv_top)

            # 获取所有可见的TextView元素文本作为补充
            textview_texts = self.get_response_from_visible_textviews()
            if textview_texts:
                log.debug(f"获取到{len(textview_texts)}个TextView元素文本")
                all_text.extend(textview_texts)

            if not robot_text:  # robot_text 可能为空
                log.warning("尝试获取其他有效的响应文本")
                # 备用方法：尝试其他方法获取响应文本
                response_methods = [
                    self._get_response_from_text_views,
                    self._get_response_from_chat_list,
                    # self._get_response_from_page_dump
                    self._extract_text_from_check_area_dump
                ]
                for method in response_methods:
                    try:
                        response = method()
                        if response and self._is_meaningful_text(response):
                            log.info(f"✅ 获取到响应文本: {response}")
                            all_text.append(response)
                    except Exception as e:
                        log.debug(f"响应获取方法失败: {e}")
                        continue
                log.warning("未获取到有效的响应文本")
                return all_text
            return all_text

        except Exception as e:
            log.error(f"获取响应文本失败: {e}")
            return []

    def get_response_from_asr_txt(self) -> str:
        """
        从asr_txt节点获取响应:指令信息

        Returns:
            str: 获取到的文本内容，失败时返回空字符串
        """
        return self._get_element_text_with_retry(
            resource_id="com.transsion.aivoiceassistant:id/asr_text",
            element_name="asr_txt",
            validate_ai_response=True
        )

    def get_response_from_robot_text(self) -> str:
        """
        从robot_text节点获取响应：指令响应文案

        Returns:
            str: 获取到的文本内容，失败时返回空字符串
        """
        return self._get_element_text_with_retry(
            resource_id="com.transsion.aivoiceassistant:id/robot_text",
            element_name="robot_text",
            validate_ai_response=True
        )

    def get_response_from_function_name(self) -> str:
        """
        从function_name节点获取应用名称：【名称】系统应用时会存在

        Returns:
            str: 获取到的文本内容，失败时返回空字符串
        """
        return self._get_element_text_with_retry(
            resource_id="com.transsion.aivoiceassistant:id/function_name",
            element_name="function_name",
            validate_ai_response=True
        )

    def get_response_from_function_control(self) -> str:
        """
        从function_control节点获取响应：【状态】系统应用时会存在

        Returns:
            str: 获取到的文本内容，失败时返回空字符串
        """
        return self._get_element_text_with_retry(
            resource_id="com.transsion.aivoiceassistant:id/function_control",
            element_name="function_control",
            validate_ai_response=True
        )

    def get_response_from_tv_card_chat_gpt(self) -> str:
        """
        从tv_card_chat_gpt节点获取响应：卡片式聊天内容

        Returns:
            str: 获取到的文本内容，失败时返回空字符串
        """
        return self._get_element_text_with_retry(
            resource_id="com.transsion.aivoiceassistant:id/tv_card_chat_gpt",
            element_name="tv_card_chat_gpt",
            validate_ai_response=False  # 卡片内容可能不符合标准AI响应格式
        )

    def get_response_from_tv_top(self) -> str:
        """
        从tv_top节点获取响应：顶部文本内容

        Returns:
            str: 获取到的文本内容，失败时返回空字符串
        """
        return self._get_element_text_with_retry(
            resource_id="com.transsion.aivoiceassistant:id/tv_top",
            element_name="tv_top",
            validate_ai_response=False  # 顶部内容可能不符合标准AI响应格式
        )

    def get_response_from_textview_elements(self) -> list:
        """
        从所有TextView元素获取响应文本

        Returns:
            list: 所有TextView元素的文本内容列表
        """
        return self._get_textview_elements_text()

    def get_response_from_visible_textviews(self) -> list:
        """
        从可见的TextView元素获取响应文本

        Returns:
            list: 可见TextView元素的文本内容列表
        """
        return self._get_textview_elements_text(visible_only=True)

    def get_response_from_textview_by_text(self, text_pattern: str) -> str:
        """
        根据文本内容匹配获取TextView元素的文本

        Args:
            text_pattern: 要匹配的文本模式

        Returns:
            str: 匹配到的TextView元素文本，失败时返回空字符串
        """
        return self._get_textview_by_text_pattern(text_pattern)

    def get_response_from_textview_by_index(self, index: int = 0) -> str:
        """
        根据索引获取TextView元素的文本

        Args:
            index: TextView元素的索引，默认为0（第一个）

        Returns:
            str: 指定索引的TextView元素文本，失败时返回空字符串
        """
        return self._get_textview_by_index(index)

    def _get_element_text_with_retry(self, resource_id: str, element_name: str,
                                     max_retries: int = 3, retry_delay: float = 0.5,
                                     validate_ai_response: bool = True) -> str:
        """
        通用的元素文本获取方法，带重试机制

        Args:
            resource_id: 元素的资源ID
            element_name: 元素名称（用于日志）
            max_retries: 最大重试次数
            retry_delay: 重试间隔（秒）
            validate_ai_response: 是否验证AI响应格式

        Returns:
            str: 获取到的文本内容，失败时返回空字符串
        """
        for attempt in range(max_retries):
            try:
                log.debug(f"尝试从{element_name}节点获取响应 (第{attempt + 1}次)")

                # 获取元素对象
                element = self.driver(resourceId=resource_id)

                # 检查元素是否存在
                if not element.exists():
                    log.debug(f"{element_name}节点不存在 (第{attempt + 1}次尝试)")
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay)
                        continue
                    else:
                        log.warning(f"{element_name}节点不存在，已达到最大重试次数")
                        return ""

                # 获取文本内容
                text = element.get_text()
                log.debug(f'从{element_name}节点获取到原始文本: "{text}"')

                # 验证文本内容
                if not text:
                    log.debug(f"{element_name}节点文本为空 (第{attempt + 1}次尝试)")
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay)
                        continue
                    else:
                        log.warning(f"{element_name}节点文本为空，已达到最大重试次数")
                        return ""

                # 清理和验证文本
                cleaned_text = text.strip()
                if not cleaned_text:
                    log.debug(f"{element_name}节点文本清理后为空 (第{attempt + 1}次尝试)")
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay)
                        continue
                    else:
                        log.warning(f"{element_name}节点文本清理后为空，已达到最大重试次数")
                        return ""

                # 验证是否为有效的AI响应（如果需要）
                if validate_ai_response:
                    if self._is_ai_response(cleaned_text):
                        log.info(f"✅ 从{element_name}成功获取响应: {cleaned_text}")
                        return cleaned_text
                    else:
                        log.debug(f"{element_name}文本不符合AI响应格式: {cleaned_text} (第{attempt + 1}次尝试)")
                        if attempt < max_retries - 1:
                            time.sleep(retry_delay)
                            continue
                        else:
                            log.warning(f"{element_name}文本不符合AI响应格式: {cleaned_text}，已达到最大重试次数")
                            return cleaned_text  # 即使不符合格式也返回，让调用方决定如何处理
                else:
                    # 不验证AI响应格式，直接返回清理后的文本
                    log.info(f"✅ 从{element_name}成功获取文本: {cleaned_text}")
                    return cleaned_text

            except Exception as e:
                log.debug(f"从{element_name}获取响应异常 (第{attempt + 1}次尝试): {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
                else:
                    log.error(f"从{element_name}获取响应失败，已达到最大重试次数: {e}")
                    return ""

        # 理论上不会到达这里，但为了安全起见
        log.warning(f"从{element_name}获取响应未知错误")
        return ""

    def _get_response_from_check_area(self) -> str:
        """
        从指定的check_area节点获取所有文本信息

        Returns:
            str: 从check_area节点提取的文本内容
        """
        try:
            log.info("从check_area节点获取响应文本")

            # 查找指定的check_area节点
            check_area = self.driver(resourceId="com.transsion.aivoiceassistant:id/robot_text")

            if not check_area.exists():
                log.debug("check_area节点不存在")
                return ""

            # 方法1: 直接获取节点的所有文本
            all_text = check_area.get_text()
            if all_text and all_text.strip():
                log.info(f"从check_area直接获取文本: {all_text.strip()}")
                return all_text.strip()

            # 方法2: 查找子节点中的robot_text
            robot_text_element = check_area.child(resourceId="com.transsion.aivoiceassistant:id/robot_text")
            if robot_text_element.exists():
                robot_text = robot_text_element.get_text()
                if robot_text and robot_text.strip():
                    log.info(f"从robot_text子节点获取文本: {robot_text.strip()}")
                    return robot_text.strip()

            # 方法3: 遍历check_area下的所有TextView子节点
            text_views = check_area.child(className="android.widget.TextView")
            if text_views.exists():
                collected_texts = []
                for i in range(text_views.count):
                    text = text_views[i].get_text()
                    if text and text.strip():
                        collected_texts.append(text.strip())

                if collected_texts:
                    combined_text = " ".join(collected_texts)
                    log.info(f"从TextView子节点收集文本: {combined_text}")
                    return combined_text

            # 方法4: 使用dump_hierarchy解析check_area节点
            response_text = self._extract_text_from_check_area_dump()
            if response_text:
                log.info(f"从dump解析获取文本: {response_text}")
                return response_text

            log.debug("check_area节点中未找到有效文本")
            return ""

        except Exception as e:
            log.debug(f"从check_area获取响应失败: {e}")
            return ""

    def _extract_text_from_check_area_dump(self) -> str:
        """
        从页面dump中提取check_area节点的文本信息
        使用正则表达式直接提取XML中text=""属性的文本内容

        Returns:
            str: 提取的文本内容
        """
        try:
            # 获取页面dump
            dump = self.driver.dump_hierarchy()
            if not dump:
                return ""

            # 使用优化的正则表达式方法提取文本
            extracted_texts = self._extract_text_attributes_from_xml(dump)

            if extracted_texts:
                combined_text = " ".join(extracted_texts)
                log.info(f"从dump正则提取文本: {combined_text}")
                return combined_text

            return ""

        except Exception as e:
            log.debug(f"从dump解析check_area失败: {e}")
            return ""

    def _extract_text_attributes_from_xml(self, xml_content: str) -> list:
        """
        使用正则表达式从XML中提取所有text=""属性中的文本内容

        Args:
            xml_content: XML字符串内容

        Returns:
            list: 提取到的文本列表
        """
        import re

        try:
            # 正则表达式模式：匹配 text="..." 中双引号内的内容
            # 使用非贪婪匹配，支持转义字符
            text_pattern = r'text="([^"]*)"'

            # 查找所有匹配的文本
            matches = re.findall(text_pattern, xml_content)

            # 过滤和清理文本
            extracted_texts = []
            for text in matches:
                # 清理文本：去除首尾空白，处理转义字符
                cleaned_text = self._clean_extracted_text(text)
                if cleaned_text:  # 只保留非空文本
                    extracted_texts.append(cleaned_text)

            log.debug(f"正则提取到 {len(extracted_texts)} 个文本: {extracted_texts[:10]}...")  # 只显示前10个
            return extracted_texts

        except Exception as e:
            log.debug(f"正则提取文本失败: {e}")
            return []

    def _clean_extracted_text(self, text: str) -> str:
        """
        清理从XML中提取的文本

        Args:
            text: 原始文本

        Returns:
            str: 清理后的文本
        """
        if not text:
            return ""

        # 处理常见的XML转义字符
        text = text.replace('&lt;', '<')
        text = text.replace('&gt;', '>')
        text = text.replace('&amp;', '&')
        text = text.replace('&quot;', '"')
        text = text.replace('&apos;', "'")

        # 去除首尾空白
        text = text.strip()

        # 过滤掉过短或无意义的文本
        if len(text) < 1:
            return ""

        # 过滤掉纯数字或纯符号的文本（可选）
        if text.isdigit() or not any(c.isalpha() or c.isdigit() for c in text):
            return ""

        return text

    def extract_all_text_from_xml(self, xml_content: str, filter_keywords: list = None) -> list:
        """
        通用方法：从XML中提取所有text=""属性的文本内容

        Args:
            xml_content: XML字符串内容
            filter_keywords: 可选的过滤关键词列表，只返回包含这些关键词的文本

        Returns:
            list: 提取到的文本列表
        """
        try:
            extracted_texts = self._extract_text_attributes_from_xml(xml_content)

            # 如果指定了过滤关键词，进行过滤
            if filter_keywords:
                filtered_texts = []
                for text in extracted_texts:
                    text_lower = text.lower()
                    if any(keyword.lower() in text_lower for keyword in filter_keywords):
                        filtered_texts.append(text)
                log.debug(f"关键词过滤后保留 {len(filtered_texts)} 个文本")
                return filtered_texts

            return extracted_texts

        except Exception as e:
            log.debug(f"提取XML文本失败: {e}")
            return []

    def extract_text_by_resource_id_from_xml(self, xml_content: str, resource_id: str) -> list:
        """
        从XML中提取指定resource-id节点的text=""属性文本

        Args:
            xml_content: XML字符串内容
            resource_id: 目标resource-id

        Returns:
            list: 提取到的文本列表
        """
        import re

        try:
            # 构建匹配指定resource-id节点的正则表达式
            # 使用更灵活的模式，支持属性顺序变化和换行
            escaped_id = re.escape(resource_id)

            # 方法1: resource-id在text之前
            pattern1 = rf'<node[^>]*resource-id="{escaped_id}"[^>]*text="([^"]*)"[^>]*>'
            # 方法2: text在resource-id之前
            pattern2 = rf'<node[^>]*text="([^"]*)"[^>]*resource-id="{escaped_id}"[^>]*>'

            matches = []
            matches.extend(re.findall(pattern1, xml_content, re.DOTALL))
            matches.extend(re.findall(pattern2, xml_content, re.DOTALL))

            # 清理提取的文本
            cleaned_texts = []
            for text in matches:
                cleaned_text = self._clean_extracted_text(text)
                if cleaned_text:
                    cleaned_texts.append(cleaned_text)

            log.debug(f"从resource-id '{resource_id}' 提取到 {len(cleaned_texts)} 个文本")
            return cleaned_texts

        except Exception as e:
            log.debug(f"按resource-id提取文本失败: {e}")
            return []

    def _get_response_from_text_views(self) -> str:
        """从TextView元素获取响应"""
        try:
            text_views = self.driver(className="android.widget.TextView")
            log.info("从TextView元素获取响应")
            if text_views.exists():
                for i in range(text_views.count):
                    text = text_views[i].get_text()
                    if text and self._is_ai_response(text):
                        log.info(f"从TextView获取响应: {text.strip()}")
                        return text.strip()
        except Exception as e:
            log.debug(f"从TextView获取响应失败: {e}")
        return ""

    def _get_response_from_chat_list(self) -> str:
        """从聊天列表获取响应"""
        try:
            # 查找RecyclerView中的最新消息
            recycler_view = self.driver(className="androidx.recyclerview.widget.RecyclerView")
            log.info('查找RecyclerView中的最新消息')
            if recycler_view.exists():
                # 获取所有子元素的文本
                all_text = recycler_view.get_text()
                if all_text:
                    # 分割文本并查找AI响应
                    lines = all_text.split('\n')
                    for line in reversed(lines):  # 从最新的开始
                        if line.strip() and self._is_ai_response(line.strip()):
                            log.info(f'查找RecyclerView中的文案{line.strip()}')
                            return line.strip()
        except Exception as e:
            log.debug(f"从聊天列表获取响应失败: {e}")
        return ""

    def _get_response_from_page_dump(self) -> str:
        """从页面dump获取响应"""
        try:
            # 获取页面所有文本
            page_text = self._get_page_text_snapshot()
            # log.info(f"页面所有文本: {page_text}")
            if page_text:
                lines = page_text.split('\n')
                for line in reversed(lines):
                    if line.strip() and self._is_ai_response(line.strip()):
                        log.info(f"从页面dump获取响应: {line.strip()}")
                        return line.strip()
        except Exception as e:
            log.debug(f"从页面dump获取响应失败: {e}")
        return ""

    def _is_ai_response(self, text: str) -> bool:
        """
        判断文本是否是AI响应
        
        Args:
            text: 要检查的文本
            
        Returns:
            bool: 是否是AI响应
        """
        try:
            if not text or len(text.strip()) < 2:
                return False

            text = text.strip()
            text_lower = text.lower()

            # 过滤掉用户输入的命令
            user_commands = ["open bluetooth", "close bluetooth", "open contacts", "what time", "hello"]
            if any(cmd in text_lower for cmd in user_commands):
                return False

            # 过滤掉固定的UI文本
            ui_texts = [
                "hi，我是ella", "我可以为你", "换一换", "有问题尽管问我",
                "昨天", "今天", ":", "ag600", "足球表面", "中欧班列", "deepseek"
            ]
            if any(ui_text in text_lower for ui_text in ui_texts):
                return False

            # 检查蓝牙相关响应（优先级最高）
            bluetooth_patterns = [
                "蓝牙.*已.*打开", "蓝牙.*已.*开启", "蓝牙.*已.*关闭",
                "bluetooth.*on", "bluetooth.*off", "bluetooth.*enabled", "bluetooth.*disabled",
                "蓝牙 已打开", "蓝牙已打开", "蓝牙 已关闭", "蓝牙已关闭"
            ]

            # 检查联系人相关响应
            contacts_patterns = [
                "联系人.*已.*打开", "联系人.*已.*开启", "通讯录.*已.*打开", "通讯录.*已.*开启",
                "contacts.*opened", "contacts.*launched", "contact.*app.*opened",
                "联系人 已打开", "联系人已打开", "通讯录 已打开", "通讯录已打开",
                "正在打开联系人", "正在打开通讯录", "opening contacts"
            ]

            # 检查通用完成响应
            done_patterns = [
                "done", "Done", "DONE", "完成", "好的", "已完成"
            ]

            for pattern in bluetooth_patterns:
                if re.search(pattern, text_lower):
                    log.info(f"匹配到蓝牙响应模式: {pattern} -> {text}")
                    return True

            for pattern in contacts_patterns:
                if re.search(pattern, text_lower):
                    log.info(f"匹配到联系人响应模式: {pattern} -> {text}")
                    return True

            for pattern in done_patterns:
                if pattern.lower() in text_lower:
                    log.info(f"匹配到完成响应模式: {pattern} -> {text}")
                    return True

            # 检查AI响应的常见特征
            ai_indicators = [
                "已", "成功", "失败", "正在", "无法", "请", "您好", "抱歉",
                "我", "为您", "帮您", "可以", "不能", "设置", "打开", "关闭"
            ]

            # 至少包含一个AI指示词且长度合适
            if any(indicator in text for indicator in ai_indicators) and len(text) >= 3:
                # 排除太短的文本（除非是明确的状态响应）
                if len(text) >= 5 or any(
                        keyword in text_lower for keyword in ["蓝牙", "bluetooth", "联系人", "通讯录", "contacts"]):
                    log.info(f"匹配到AI响应特征: {text}")
                    return True

            return False

        except Exception as e:
            log.debug(f"判断AI响应失败: {e}")
            return False

    def _is_meaningful_text(self, text: str) -> bool:
        """
        判断文本是否有意义
        
        Args:
            text: 要检查的文本
            
        Returns:
            bool: 文本是否有意义
        """
        if not text or len(text.strip()) < 2:
            return False

        text = text.strip()

        # 过滤掉纯数字、纯符号等无意义文本
        if text.isdigit() or not any(c.isalpha() for c in text):
            return False

        # 过滤掉过短的文本
        if len(text) < 3:
            return False

        return True

    def verify_command_in_response(self, command: str, response: str) -> bool:
        """
        验证响应中是否包含命令内容
        
        Args:
            command: 原始命令
            response: AI响应
            
        Returns:
            bool: 响应是否包含命令相关内容
        """
        try:
            if not command or not response:
                return False

            command_lower = command.lower()
            response_lower = response.lower()

            # 提取命令中的关键词
            command_keywords = []
            if "bluetooth" in command_lower or "蓝牙" in command_lower:
                command_keywords.extend(["bluetooth", "蓝牙"])
            if "contacts" in command_lower or "联系人" in command_lower:
                command_keywords.extend(["contacts", "联系人", "通讯录"])
            if "open" in command_lower or "打开" in command_lower:
                command_keywords.extend(["open", "打开", "启动", "开启"])

            # 检查响应中是否包含这些关键词
            for keyword in command_keywords:
                if keyword in response_lower:
                    log.info(f"响应包含命令关键词: {keyword}")
                    return True

            return False

        except Exception as e:
            log.error(f"验证命令响应失败: {e}")
            return False

    def _get_quick_element_count(self) -> int:
        """获取页面元素快速计数"""
        try:
            if not self.driver:
                return 0
            # 简单计数页面中的TextView元素
            text_views = self.driver(className="android.widget.TextView")
            return text_views.count if text_views.exists() else 0
        except Exception:
            return 0

    def _get_page_text_snapshot(self) -> str:
        """获取页面文本快照"""
        try:
            if not self.driver:
                return ""
            # 获取页面dump并提取文本
            dump = self.driver.dump_hierarchy()
            extracted_texts = self._extract_text_attributes_from_xml(dump)
            return dump if dump else ""
        except Exception:
            return ""

    def _quick_check_for_response(self, initial_count: int) -> bool:
        """快速检查是否有新响应"""
        try:
            current_count = self._get_quick_element_count()
            return current_count > initial_count
        except Exception:
            return False

    def _check_for_new_response(self, initial_snapshot: str) -> bool:
        """检查是否有新的响应文本"""
        try:
            current_snapshot = self._get_page_text_snapshot()
            return len(current_snapshot) > len(initial_snapshot)
        except Exception:
            return False

    def _check_tts_button_appeared(self) -> bool:
        """检查TTS播放按钮是否出现"""
        try:
            if not self.driver:
                return False
            # 查找可能的TTS按钮
            tts_selectors = [
                self.driver(resourceId="com.transsion.aivoiceassistant:id/iv_tts_play"),
                self.driver(description="播放"),
                self.driver(description="Play"),
                self.driver(className="android.widget.ImageView")
            ]

            for selector in tts_selectors:
                if selector.exists():
                    return True
            return False
        except Exception:
            return False

    def _ensure_on_ella_page(self) -> bool:
        """
        确保当前在Ella页面
        如果不在Ella页面，尝试返回到Ella页面

        Returns:
            bool: 是否成功确保在Ella页面
        """
        try:
            log.info("检查是否在Ella页面...")

            # 如果没有状态检查器，跳过检查
            if not self.status_checker:
                log.warning("状态检查器未初始化，跳过页面状态检查")
                return True

            # 检查当前是否在Ella进程
            if self.status_checker.ensure_ella_process():
                log.info("✅ 当前在Ella页面")
                return True

            log.warning("当前不在Ella页面，尝试返回Ella...")

            # 尝试返回到Ella页面
            if self._return_to_ella_app():
                log.info("✅ 成功返回到Ella页面")
                return True

            log.error("❌ 无法返回到Ella页面")
            return False

        except Exception as e:
            log.error(f"确保在Ella页面失败: {e}")
            return False

    def _return_to_ella_app(self) -> bool:
        """
        返回到Ella应用

        Returns:
            bool: 是否成功返回Ella应用
        """
        try:
            if not self.driver:
                log.error("驱动实例未初始化")
                return False

            log.info("尝试返回Ella应用...")

            # 方法1: 多次按返回键尝试回到Ella
            max_back_attempts = 3
            for i in range(max_back_attempts):
                log.info(f"第{i+1}次按返回键...")
                self.driver.press("back")
                time.sleep(1)

                # 检查是否回到了Ella
                if self.status_checker and self.status_checker.ensure_ella_process():
                    log.info(f"✅ 通过返回键回到Ella应用 (第{i+1}次)")
                    return True

            # 方法2: 重新启动Ella应用
            log.info("尝试重新启动Ella应用...")
            if self._start_ella_app():
                log.info("✅ 重新启动Ella应用成功")
                return True

            log.error("❌ 无法返回Ella应用")
            return False

        except Exception as e:
            log.error(f"返回Ella应用失败: {e}")
            return False

    def _start_ella_app(self) -> bool:
        """
        启动Ella应用

        Returns:
            bool: 是否成功启动
        """
        try:
            if not self.driver:
                return False

            # 尝试启动Ella应用
            ella_packages = [
                "com.transsion.aivoiceassistant",
                "com.transsion.ella"
            ]

            for package in ella_packages:
                try:
                    self.driver.app_start(package)
                    time.sleep(2)

                    # 检查是否启动成功
                    if self.status_checker and self.status_checker.ensure_ella_process():
                        log.info(f"✅ 成功启动Ella应用: {package}")
                        return True
                except Exception as e:
                    log.debug(f"启动{package}失败: {e}")
                    continue

            return False

        except Exception as e:
            log.error(f"启动Ella应用失败: {e}")
            return False


    def _get_textview_elements_text(self, visible_only: bool = False, max_elements: int = 50) -> list:
        """
        获取所有TextView元素的文本内容

        Args:
            visible_only: 是否只获取可见元素
            max_elements: 最大获取元素数量，防止性能问题

        Returns:
            list: TextView元素文本内容列表
        """
        try:
            log.debug(f"开始获取TextView元素文本 (仅可见: {visible_only})")

            # 获取所有TextView元素
            if visible_only:
                textviews = self.driver(className="android.widget.TextView").filter(lambda elem: elem.info.get('visible', False))
            else:
                textviews = self.driver(className="android.widget.TextView")

            if not textviews.exists():
                log.debug("未找到任何TextView元素")
                return []

            text_list = []
            count = 0

            # 遍历TextView元素获取文本
            for textview in textviews:
                if count >= max_elements:
                    log.debug(f"已达到最大元素数量限制: {max_elements}")
                    break

                try:
                    text = textview.get_text()
                    if text and text.strip():  # 过滤空文本
                        text_list.append(text.strip())
                        log.debug(f"获取到TextView文本: '{text.strip()}'")
                    count += 1
                except Exception as e:
                    log.debug(f"获取TextView文本失败: {e}")
                    continue

            log.info(f"✅ 成功获取{len(text_list)}个TextView元素文本")
            return text_list

        except Exception as e:
            log.error(f"获取TextView元素文本失败: {e}")
            return []

    def _get_textview_by_text_pattern(self, text_pattern: str, max_retries: int = 3) -> str:
        """
        根据文本模式匹配获取TextView元素文本

        Args:
            text_pattern: 要匹配的文本模式
            max_retries: 最大重试次数

        Returns:
            str: 匹配到的TextView元素文本
        """
        for attempt in range(max_retries):
            try:
                log.debug(f"尝试匹配TextView文本模式: '{text_pattern}' (第{attempt + 1}次)")

                # 使用textContains进行模糊匹配
                textview = self.driver(className="android.widget.TextView", textContains=text_pattern)

                if textview.exists():
                    text = textview.get_text()
                    log.info(f"✅ 成功匹配到TextView文本: '{text}'")
                    return text

                # 如果精确匹配失败，尝试遍历所有TextView进行模糊匹配
                textviews = self.driver(className="android.widget.TextView")
                for tv in textviews:
                    try:
                        tv_text = tv.get_text()
                        if tv_text and text_pattern.lower() in tv_text.lower():
                            log.info(f"✅ 通过遍历匹配到TextView文本: '{tv_text}'")
                            return tv_text
                    except:
                        continue

                log.debug(f"未找到匹配的TextView文本 (第{attempt + 1}次)")
                if attempt < max_retries - 1:
                    time.sleep(0.5)

            except Exception as e:
                log.debug(f"匹配TextView文本失败: {e} (第{attempt + 1}次)")
                if attempt < max_retries - 1:
                    time.sleep(0.5)

        log.warning(f"⚠️ 未能匹配到包含'{text_pattern}'的TextView文本")
        return ""

    def _get_textview_by_index(self, index: int, max_retries: int = 3) -> str:
        """
        根据索引获取TextView元素文本

        Args:
            index: TextView元素索引
            max_retries: 最大重试次数

        Returns:
            str: 指定索引的TextView元素文本
        """
        for attempt in range(max_retries):
            try:
                log.debug(f"尝试获取第{index}个TextView元素文本 (第{attempt + 1}次)")

                textviews = self.driver(className="android.widget.TextView")

                if not textviews.exists():
                    log.debug(f"未找到任何TextView元素 (第{attempt + 1}次)")
                    if attempt < max_retries - 1:
                        time.sleep(0.5)
                        continue
                    else:
                        return ""

                # 检查索引是否有效
                textview_count = len(textviews)
                if index >= textview_count:
                    log.warning(f"⚠️ 索引{index}超出范围，总共有{textview_count}个TextView元素")
                    return ""

                # 获取指定索引的TextView文本
                textview = textviews[index]
                text = textview.get_text()

                if text:
                    log.info(f"✅ 成功获取第{index}个TextView文本: '{text}'")
                    return text
                else:
                    log.debug(f"第{index}个TextView文本为空 (第{attempt + 1}次)")
                    if attempt < max_retries - 1:
                        time.sleep(0.5)
                        continue

            except Exception as e:
                log.debug(f"获取第{index}个TextView文本失败: {e} (第{attempt + 1}次)")
                if attempt < max_retries - 1:
                    time.sleep(0.5)

        log.warning(f"⚠️ 未能获取第{index}个TextView元素文本")
        return ""


if __name__ == '__main__':
    from core.base_driver import driver_manager

    handler = EllaResponseHandler(driver_manager.driver)
    # print(handler.get_response_text())
    print("ASR文本:", handler.get_response_from_asr_txt())
    print("机器人文本:", handler.get_response_from_robot_text())
    print("功能名称:", handler.get_response_from_function_name())
    print("功能控制:", handler.get_response_from_function_control())
    print("卡片聊天内容:", handler.get_response_from_tv_card_chat_gpt())
    print("顶部文本:", handler.get_response_from_tv_top())
    print("所有TextView文本:", handler.get_response_from_textview_elements())
    print("可见TextView文本:", handler.get_response_from_visible_textviews())
    print("匹配TextView文本:", handler.get_response_from_textview_by_text("hello"))
    print("第一个TextView文本:", handler.get_response_from_textview_by_index(0))
