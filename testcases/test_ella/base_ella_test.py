"""
Ella测试基类
提供通用的测试方法和fixture，简化测试用例编写
"""
import pytest
import allure
import time
import json
import os
from pathlib import Path
from pages.apps.ella.dialogue_page import EllaDialoguePage
from pages.apps.ella.ella_contact_command_handler import <PERSON>ContactCommandHandler
from core.logger import log
from core.popup_tool import create_popup_tool
from tools.adb_process_monitor import AdbProcessMonitor
from tools.file_detector import FileDetector
from utils.screen_manager import ScreenManager


class BaseEllaTest:
    """Ella测试基类"""

    # 类级别的配置缓存
    _status_check_config = None
    _project_root = Path(__file__).parent.parent.parent

    # 弹窗处理配置
    _popup_handling_enabled = True  # 是否启用弹窗处理
    _popup_timeout = 3  # 弹窗检测超时时间（秒）
    _popup_check_interval = 0.3  # 弹窗检查间隔（秒）

    # 屏幕管理配置（类级别）
    _screen_manager = None  # 屏幕管理器实例
    _screen_management_enabled = True  # 是否启用屏幕管理
    _batch_test_started = False  # 是否已开始批量测试
    _original_screen_settings_saved = False  # 是否已保存原始屏幕设置

    @property
    def adb_monitor(self):
        """获取ADB进程监控器实例（懒加载）"""
        if not hasattr(self, '_adb_monitor'):
            self._adb_monitor = AdbProcessMonitor(cache_duration=5)
        return self._adb_monitor

    @classmethod
    def get_screen_manager(cls):
        """获取屏幕管理器实例（类级别单例）"""
        if cls._screen_manager is None:
            cls._screen_manager = ScreenManager()
            log.debug("✅ 屏幕管理器初始化成功")
        return cls._screen_manager

    @classmethod
    def setup_batch_test_screen(cls):
        """
        批量测试开始前设置屏幕为常亮
        仅在第一次调用时执行设置
        """
        if not cls._screen_management_enabled:
            log.info("🔧 屏幕管理已禁用，跳过屏幕设置")
            return

        if cls._batch_test_started:
            log.debug("📱 批量测试屏幕设置已完成，跳过重复设置")
            return

        try:
            log.info("🚀 开始批量测试屏幕设置...")
            screen_manager = cls.get_screen_manager()

            # 设置屏幕永不超时
            success1 = screen_manager.set_screen_never_timeout()

            # 启用充电时保持常亮
            success2 = screen_manager.enable_stay_on_while_plugged()

            if success1 and success2:
                cls._batch_test_started = True
                cls._original_screen_settings_saved = True
                log.info("✅ 批量测试屏幕设置完成：屏幕永不超时，充电时保持常亮")

                # 记录当前状态
                status = screen_manager.get_screen_status_info()
                log.info(f"📊 当前屏幕状态: 超时={status['screen_timeout_description']}, "
                         f"充电常亮={status['stay_on_description']}")
            else:
                log.warning("⚠️ 批量测试屏幕设置部分失败")

        except Exception as e:
            log.error(f"❌ 批量测试屏幕设置异常: {e}")

    @classmethod
    def restore_batch_test_screen(cls):
        """
        批量测试结束后恢复屏幕设置为10分钟超时
        """
        if not cls._screen_management_enabled:
            log.info("🔧 屏幕管理已禁用，跳过屏幕恢复")
            return

        if not cls._batch_test_started:
            log.debug("📱 批量测试未开始，无需恢复屏幕设置")
            return

        try:
            log.info("🔄 开始恢复批量测试屏幕设置...")
            screen_manager = cls.get_screen_manager()

            # 设置屏幕10分钟后超时
            success1 = screen_manager.set_screen_timeout_minutes(10)

            # 禁用充电时保持常亮
            success2 = screen_manager.disable_stay_on_while_plugged()

            if success1 and success2:
                log.info("✅ 批量测试屏幕设置恢复完成：10分钟超时，关闭充电时常亮")

                # 记录恢复后状态
                status = screen_manager.get_screen_status_info()
                log.info(f"📊 恢复后屏幕状态: 超时={status['screen_timeout_description']}, "
                         f"充电常亮={status['stay_on_description']}")
            else:
                log.warning("⚠️ 批量测试屏幕设置恢复部分失败")

            # 重置标志
            cls._batch_test_started = False
            cls._original_screen_settings_saved = False

        except Exception as e:
            log.error(f"❌ 批量测试屏幕设置恢复异常: {e}")

    @classmethod
    def set_screen_management_config(cls, enabled: bool = True):
        """
        设置屏幕管理配置

        Args:
            enabled: 是否启用屏幕管理
        """
        cls._screen_management_enabled = enabled
        log.info(f"🔧 屏幕管理配置更新: 启用={enabled}")

    @classmethod
    def get_screen_status(cls):
        """
        获取当前屏幕状态信息

        Returns:
            dict: 屏幕状态信息
        """
        try:
            screen_manager = cls.get_screen_manager()
            return screen_manager.get_screen_status_info()
        except Exception as e:
            log.error(f"❌ 获取屏幕状态失败: {e}")
            return {}

    def _get_popup_tool(self, ella_app):
        """获取弹窗处理工具实例（懒加载）"""
        if not hasattr(self, '_popup_tool') or self._popup_tool is None:
            try:
                # 使用ella_app的driver创建弹窗工具
                self._popup_tool = create_popup_tool(ella_app.driver)
                log.debug("✅ 弹窗处理工具初始化成功")
            except Exception as e:
                log.warning(f"⚠️ 弹窗处理工具初始化失败: {e}")
                self._popup_tool = None
        return self._popup_tool

    def _handle_popup_after_command(self, ella_app, command: str):
        """
        在执行命令后处理弹窗 - 使用最新优化的弹窗处理方法

        Args:
            ella_app: Ella应用实例
            command: 执行的命令
        """
        log.info('handle_popup_after_command:处理弹窗')
        if not self._popup_handling_enabled:
            return

        try:
            popup_tool = self._get_popup_tool(ella_app)
            if popup_tool is None:
                return

            # 使用优化后的单次检测方法（最快速、最可靠）
            success = popup_tool.detect_and_close_popup_once(debug_mode=False)

            if success:
                log.info(f"✅ 弹窗检测并关闭成功，命令: {command}")
            else:
                log.debug(f"ℹ️ 未检测到弹窗或无需处理，命令: {command}")

        except Exception as e:
            # 弹窗处理失败不应影响主业务流程
            log.warning(f"⚠️ 弹窗处理异常（不影响主流程），命令: {command}, 错误: {e}")

    @classmethod
    def set_popup_handling_config(cls, enabled: bool = True, timeout: int = 3, check_interval: float = 0.3):
        """
        设置弹窗处理配置

        Args:
            enabled: 是否启用弹窗处理
            timeout: 弹窗检测超时时间（秒）
            check_interval: 弹窗检查间隔（秒）
        """
        cls._popup_handling_enabled = enabled
        cls._popup_timeout = timeout
        cls._popup_check_interval = check_interval
        log.info(f"🔧 弹窗处理配置更新: 启用={enabled}, 超时={timeout}s, 间隔={check_interval}s")

    def ultra_fast_popup_check(self, ella_app):
        """
        超快速弹窗检测（仅检测，不关闭）

        Args:
            ella_app: Ella应用实例

        Returns:
            bool: 是否检测到弹窗
        """
        try:
            popup_tool = self._get_popup_tool(ella_app)
            if popup_tool is None:
                return False

            # 使用超快速检测方法
            has_popup = popup_tool.is_popup_present_ultra_fast()
            log.debug(f"超快速弹窗检测结果: {'有弹窗' if has_popup else '无弹窗'}")
            return has_popup

        except Exception as e:
            log.warning(f"⚠️ 超快速弹窗检测异常: {e}")
            return False

    def smart_popup_handling(self, ella_app, command: str = "", wait_for_popup: bool = False):
        """
        智能弹窗处理 - 根据情况选择最合适的处理方式

        Args:
            ella_app: Ella应用实例
            command: 执行的命令（用于日志）
            wait_for_popup: 是否等待弹窗出现

        Returns:
            bool: 是否成功处理弹窗
        """
        try:
            popup_tool = self._get_popup_tool(ella_app)
            if popup_tool is None:
                return False

            if wait_for_popup:
                # 需要等待弹窗的情况，使用传统方法
                log.info(f"智能弹窗处理（等待模式）: {command}")
                success = popup_tool.detect_and_close_popup(
                    timeout=self._popup_timeout,
                    wait_for_popup=True
                )
            else:
                # 不等待弹窗的情况，使用单次检测
                log.info(f"智能弹窗处理（快速模式）: {command}")
                success = popup_tool.detect_and_close_popup_once()

            if success:
                log.info(f"✅ 智能弹窗处理成功: {command}")
            else:
                log.debug(f"ℹ️ 智能弹窗处理完成（无弹窗）: {command}")

            return success

        except Exception as e:
            log.warning(f"⚠️ 智能弹窗处理异常: {command}, 错误: {e}")
            return False

    @classmethod
    def _load_status_check_config(cls):
        """按需加载状态检查配置"""
        if cls._status_check_config is None:
            try:
                config_path = cls._project_root / "config" / "status_check_config.json"
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    cls._status_check_config = config_data.get("status_check_config", {})
                log.debug(f"✅ 已加载状态检查配置，共 {len(cls._status_check_config)} 项")
            except Exception as e:
                log.error(f"❌ 加载状态检查配置失败: {e}")
                cls._status_check_config = {}
        return cls._status_check_config

    def clear_all_running_processes(self):
        """
        清除手机上所有运行中的应用进程
        使用AdbProcessMonitor进行进程清理
        """
        try:
            log.info("🧹 开始清除手机上所有运行中的应用进程...")
            self.adb_monitor.clear_all_running_processes()
            log.info("✅ 应用进程清理完成")
        except Exception as e:
            log.error(f"❌ 清理应用进程失败: {e}")

    def clear_recent_apps(self):
        """
        清理Recent页面应用
        使用AdbProcessMonitor进行Recent页面清理
        """
        try:
            log.info("🎯 开始清理Recent页面应用...")
            return self.adb_monitor.clear_recent_apps()
        except Exception as e:
            log.error(f"❌ Recent页面清理异常: {e}")
            return 0

    @pytest.fixture(scope="function")
    def ella_app(self):
        """简化的Ella应用fixture"""
        # 在第一个测试用例开始前设置屏幕
        self.setup_batch_test_screen()

        ella_page = EllaDialoguePage()

        try:
            # 在启动应用前清除所有运行中的进程
            self.clear_all_running_processes()

            # 启动应用
            assert ella_page.start_app(), "Ella应用启动失败"
            assert ella_page.wait_for_page_load(timeout=15), "Ella页面加载失败"

            log.info("✅ Ella应用启动成功")
            yield ella_page

        except Exception as e:
            log.error(f"❌ Ella应用启动异常: {e}")
            pytest.fail(f"Ella应用启动异常: {e}")
        finally:
            # 清理
            try:
                ella_page.stop_app()
            except Exception as e:
                log.warning(f"⚠️ 停止应用异常: {e}")

    def execute_command_and_verify(self, ella_app, command: str, expected_status_change: bool = True,
                                   verify_files: bool = False, multimodal_type: str = None):
        """
        执行命令并验证结果的通用方法

        Args:
            ella_app: Ella应用实例
            command: 要执行的命令
            expected_status_change: 是否期望状态发生变化
            verify_files: 是否验证文件
            multimodal_type: 多模态类型 (document/gallery/camera/ai_image_generator)，默认为None

        Returns:
            tuple: (initial_status, final_status, response_text, files_status)
        """
        # 支持的多模态类型
        supported_multimodal_types = ["document", "gallery", "camera", "ai_image_generator"]

        # 记录初始状态
        initial_status = self._get_initial_status(ella_app, command)
        log.info(f"初始状态{initial_status}- 使用命令{command}，状态: ")

        # 确保页面就绪
        self._ensure_page_ready(ella_app)

        # 如果指定了有效的多模态类型，执行多模态操作
        multimodal_executed = False
        if multimodal_type and multimodal_type in supported_multimodal_types:
            log.info(f"🎯 检测到多模态指令: {multimodal_type}")
            multimodal_executed = self._execute_multimodal_operation(ella_app, multimodal_type)

        # 执行命令
        self._execute_command(ella_app, command)

        # 根据命令类型和多模态类型动态设置响应等待时间
        response_timeout = self._get_response_timeout(command, multimodal_type)

        # 等待响应（但不立即获取响应文本）
        response_received = ella_app.wait_for_response(timeout=response_timeout)
        if not response_received:
            log.warning(f"⚠️ 响应超时 (等待时间: {response_timeout}秒)")
            time.sleep(3)

        # 验证最终状态并获取其他应用页面信息（此时可能在目标应用页面）
        final_status, other_app_text = self._get_final_status_with_page_info(ella_app, command)

        # 在确认状态后，确保回到Ella页面获取响应文本
        response_text = self._wait_and_get_response_after_status_check(ella_app, other_app_text)

        # 验证状态变化
        if expected_status_change:
            self._verify_status_change(initial_status, final_status, command)

        files_status = False
        if verify_files:
            file_detector = FileDetector(use_adb=True)
            files_status = self._detect_and_verify_files(file_detector, command)

        # 如果执行了多模态操作，记录相关信息
        if multimodal_type and multimodal_type in supported_multimodal_types:
            if multimodal_executed:
                log.info(f"✅ 多模态操作执行完成: {multimodal_type}")
            else:
                log.warning(f"⚠️ 多模态操作执行失败: {multimodal_type}")

        return initial_status, final_status, response_text, files_status

    def _get_final_status_with_page_info(self, ella_app, command: str, wait_time: float = 3.0):
        """
        获取最终状态并收集其他应用页面信息

        Args:
            ella_app: Ella应用实例
            command: 输入命令
            wait_time: 等待状态变化的时间（秒）

        Returns:
            tuple: (final_status, other_app_text)
        """
        if command.lower() in ["take a photo", 'take a selfie']:
            wait_time = 15

        command_type = self._detect_command_type(command)

        # 等待状态变化
        if wait_time > 0:
            log.debug(f"等待状态变化: {wait_time}秒")
            time.sleep(wait_time)

        # 检查当前是否在其他应用中，如果是则获取页面文本
        other_app_text = ""
        try:
            current_app = ella_app.driver.app_current()
            current_package = current_app.get('package', '')
            log.info(f"状态检查时当前应用包名: {current_package}")

            # 如果不在Ella应用中，获取当前页面文本信息
            ella_packages = ["com.transsion.aivoiceassistant", "com.transsion.ella"]
            if current_package not in ella_packages:
                log.info(f"检测到跳转到其他应用: {current_package}，获取页面文本信息")
                other_app_text = self._get_current_page_text_info(ella_app, current_package)
                if other_app_text:
                    log.info(f"✅ 成功获取其他应用页面文本: {other_app_text[:100]}...")
                else:
                    log.warning("⚠️ 未能获取其他应用页面文本")
        except Exception as e:
            log.warning(f"检查当前应用状态失败: {e}")

        # 获取最终状态（这会自动返回Ella应用）
        final_status = self._get_status_by_type(ella_app, command_type, is_final=True, wait_time=0)

        return final_status, other_app_text

    def _get_current_page_text_info(self, ella_app, package_name: str) -> str:
        """
        获取当前页面的文本信息

        Args:
            ella_app: Ella应用实例
            package_name: 当前应用包名

        Returns:
            str: 页面文本信息
        """
        try:
            log.info(f"获取应用 {package_name} 的页面文本信息")

            # 获取页面所有TextView文本
            all_texts = []
            text_views = ella_app.driver(className="android.widget.TextView")

            if text_views.exists():
                for i in range(min(text_views.count, 50)):  # 限制最多获取50个元素
                    try:
                        tv = text_views[i] if text_views.count > 1 else text_views
                        text = tv.get_text()
                        if text and text.strip() and len(text.strip()) > 1:
                            # 过滤掉一些无意义的文本
                            if not self._is_meaningless_text(text.strip()):
                                all_texts.append(text.strip())
                    except Exception as e:
                        log.debug(f"获取TextView {i}文本失败: {e}")
                        continue

            if all_texts:
                # 合并文本，添加应用信息前缀
                combined_text = f"[{package_name}页面内容] " + " | ".join(all_texts[:20])  # 最多取前20个有效文本
                log.info(f"获取到页面文本信息，共{len(all_texts)}个有效文本元素")
                return combined_text
            else:
                log.warning("未获取到有效的页面文本")
                return f"[{package_name}页面] 未获取到文本内容"

        except Exception as e:
            log.error(f"获取页面文本信息失败: {e}")
            return f"[{package_name}页面] 获取文本失败: {str(e)}"

    def _is_meaningless_text(self, text: str) -> bool:
        """
        判断文本是否无意义

        Args:
            text: 要检查的文本

        Returns:
            bool: 是否为无意义文本
        """
        # 过滤掉一些常见的无意义文本
        meaningless_patterns = [
            r'^\d+$',  # 纯数字
            r'^[A-Z]+$',  # 纯大写字母
            r'^[a-z]+$',  # 纯小写字母（长度小于3）
            r'^\W+$',  # 纯符号
        ]

        # 常见的无意义词汇
        meaningless_words = [
            'ok', 'yes', 'no', 'on', 'off', 'up', 'down', 'in', 'out',
            'go', 'back', 'next', 'prev', 'home', 'menu', 'more', 'less'
        ]

        text_lower = text.lower().strip()

        # 检查长度
        if len(text_lower) < 2:
            return True

        # 检查是否为无意义词汇
        if text_lower in meaningless_words:
            return True

        # 检查正则模式
        import re
        for pattern in meaningless_patterns:
            if re.match(pattern, text):
                return True

        return False

    def _wait_and_get_response_after_status_check(self, ella_app, other_app_text: str = "",
                                                  max_return_attempts: int = 3):
        """
        在状态检查后获取响应文本

        Args:
            ella_app: Ella应用实例
            other_app_text: 其他应用页面文本信息
            max_return_attempts: 最大返回Ella应用尝试次数

        Returns:
            list: 响应文本列表
        """
        log.info("状态检查完成，现在获取响应文本")

        # 多次尝试确保回到Ella页面
        for attempt in range(max_return_attempts):
            log.info(f"第{attempt + 1}次尝试确保在Ella页面以获取响应")

            # 检查是否在Ella对话页面
            if ella_app.ensure_on_chat_page():
                log.info("✅ 已确认在Ella对话页面，可以获取响应")
                break

            # 如果不在，尝试返回
            log.warning(f"不在Ella对话页面，第{attempt + 1}次尝试返回")
            if ella_app.return_to_ella_app():
                time.sleep(2)
                # 再次检查是否成功返回
                if ella_app.ensure_on_chat_page():
                    log.info(f"✅ 第{attempt + 1}次尝试成功返回Ella页面")
                    break
                else:
                    log.warning(f"第{attempt + 1}次返回后仍不在Ella页面")
            else:
                log.error(f"第{attempt + 1}次返回Ella应用失败")
        else:
            # 所有尝试都失败
            log.error(f"经过{max_return_attempts}次尝试仍无法返回Ella页面，强制获取响应")

        # 获取Ella响应文本
        response_text = self._safe_get_response_text(ella_app)

        # 如果有其他应用页面文本，将其合并到响应中
        if other_app_text:
            log.info("合并其他应用页面文本到响应中")
            if isinstance(response_text, list):
                response_text.append(other_app_text)
            else:
                response_text = [response_text, other_app_text] if response_text else [other_app_text]

        log.info(f"最终获取的AI响应: '{response_text}'")
        return response_text

    @property
    def STATUS_CHECK_CONFIG(self):
        """状态检查配置映射（按需加载）"""
        return self._load_status_check_config()

    def _detect_command_type(self, command: str) -> str:
        """
        检测命令类型

        Args:
            command: 输入命令

        Returns:
            str: 命令类型，如果未识别返回None
        """
        command_lower = command.lower()

        for cmd_type, config in self.STATUS_CHECK_CONFIG.items():
            if any(keyword in command_lower for keyword in config['keywords']):
                log.debug(f"检测到命令类型: {cmd_type} ({config['description']})")
                return cmd_type

        log.debug(f"未识别的命令类型: {command}")
        return None

    def _detect_and_verify_files(self, file_detector: FileDetector, command: str) -> bool:
        """
        根据指令类型智能选择对应的文件校验方法

        Args:
            file_detector: 文件检测器实例
            command: 执行的命令

        Returns:
            bool: 文件验证结果
        """
        command_lower = command.lower()

        # 拍照相关命令
        photo_keywords = [
            'take a photo', 'take a gallery', 'capture a photo', 'snap a photo',
            'take photo', 'take gallery', 'capture photo', 'snap photo', 'take a selfie'
                                                                         'photo', 'gallery', 'camera', '拍照', '照片',
            '相机', 'selfie'
        ]

        # 截图相关命令
        screenshot_keywords = [
            'screenshot', 'screen shot', 'capture screen', 'take screenshot',
            'screen capture', '截图', '屏幕截图', '截屏'
        ]

        # 屏幕录制相关命令
        recording_keywords = [
            'screen recording', 'record screen', 'start recording', 'video recording',
            'screen record', 'record video', '屏幕录制', '录屏', '录制', 'recording'
        ]

        try:
            # 检查是否为拍照相关命令
            if any(keyword in command_lower for keyword in photo_keywords):
                log.info(f"检测到拍照相关命令: {command}")
                result = file_detector.check_recent_camera_image(time_threshold=30)
                log.info(f"相机图片检查结果: {'✅ 找到最近拍摄的照片' if result else '❌ 未找到最近拍摄的照片'}")
                return result

            # 检查是否为截图相关命令
            elif any(keyword in command_lower for keyword in screenshot_keywords):
                log.info(f"检测到截图相关命令: {command}")
                result = file_detector.check_recent_screenshot_image(time_threshold=30)
                log.info(f"截图文件检查结果: {'✅ 找到最近的截图' if result else '❌ 未找到最近的截图'}")
                return result

            # 检查是否为屏幕录制相关命令
            elif any(keyword in command_lower for keyword in recording_keywords):
                log.info(f"检测到屏幕录制相关命令: {command}")
                result = file_detector.check_recent_screen_recording(time_threshold=60)  # 录制通常需要更长时间
                log.info(f"屏幕录制检查结果: {'✅ 找到最近的录制文件' if result else '❌ 未找到最近的录制文件'}")
                return result

            # 默认情况：如果包含相机相关关键词，使用相机检查
            elif any(keyword in command_lower for keyword in ['open camera', 'start camera', 'launch camera']):
                log.info(f"检测到相机应用相关命令: {command}")
                result = file_detector.check_recent_camera_image(time_threshold=30)
                log.info(f"相机图片检查结果: {'✅ 找到最近拍摄的照片' if result else '❌ 未找到最近拍摄的照片'}")
                return result

            # 未匹配到特定类型，返回False
            else:
                log.warning(f"未识别的文件验证命令类型: {command}")
                return False

        except Exception as e:
            log.error(f"文件验证过程中发生错误: {e}")
            return False

    def _get_status_by_type(self, ella_app, command_type: str, is_final: bool = False,
                            wait_time: float = 3.0) -> any:
        """
        根据命令类型获取状态

        Args:
            ella_app: Ella应用实例
            command_type: 命令类型
            is_final: 是否为最终状态检查
            wait_time: 最终状态检查前的等待时间

        Returns:
            any: 状态值，如果无法获取返回None
        """
        if not command_type or command_type not in self.STATUS_CHECK_CONFIG:
            return None

        config = self.STATUS_CHECK_CONFIG[command_type]

        # 如果是最终状态检查，先等待状态变化
        if is_final and wait_time > 0:
            log.debug(f"等待状态变化: {wait_time}秒")
            time.sleep(wait_time)

        # 选择合适的检查方法
        method_name = config['final_method'] if is_final else config['initial_method']

        try:
            # 动态调用方法
            if hasattr(ella_app, method_name):
                method = getattr(ella_app, method_name)
                status = method()
                log.debug(f"获取{config['description']}{'(最终)' if is_final else '(初始)'}: {status}")
                return status
            else:
                log.warning(f"方法不存在: {method_name}")
                return None

        except Exception as e:
            log.error(f"获取{config['description']}失败: {e}")
            return None

    def _get_initial_status(self, ella_app, command: str):
        """
        获取初始状态

        Args:
            ella_app: Ella应用实例
            command: 输入命令

        Returns:
            any: 初始状态值
        """
        command_type = self._detect_command_type(command)
        return self._get_status_by_type(ella_app, command_type, is_final=False)

    def _get_final_status(self, ella_app, command: str, wait_time: float = 3.0):
        """
        获取最终状态（兼容性方法）

        Args:
            ella_app: Ella应用实例
            command: 输入命令
            wait_time: 等待状态变化的时间（秒）

        Returns:
            any: 最终状态值
        """
        final_status, _ = self._get_final_status_with_page_info(ella_app, command, wait_time)
        return final_status

    def get_status_by_command(self, ella_app, command: str, is_final: bool = False,
                              wait_time: float = 3.0) -> dict:
        """
        通用方法：根据命令获取状态信息

        Args:
            ella_app: Ella应用实例
            command: 输入命令
            is_final: 是否为最终状态检查
            wait_time: 最终状态检查前的等待时间

        Returns:
            dict: 包含状态信息的字典
        """
        command_type = self._detect_command_type(command)

        if not command_type:
            return {
                'command_type': None,
                'status': None,
                'description': '未识别的命令类型',
                'success': False
            }

        config = self.STATUS_CHECK_CONFIG[command_type]
        status = self._get_status_by_type(ella_app, command_type, is_final, wait_time)

        return {
            'command_type': command_type,
            'status': status,
            'description': config['description'],
            'success': status is not None,
            'is_final': is_final
        }

    def add_custom_status_check(self, command_type: str, keywords: list,
                                initial_method: str, final_method: str = None,
                                description: str = None):
        """
        添加自定义状态检查配置

        Args:
            command_type: 命令类型标识
            keywords: 关键词列表
            initial_method: 初始状态检查方法名
            final_method: 最终状态检查方法名（可选，默认与initial_method相同）
            description: 描述信息（可选）
        """
        if final_method is None:
            final_method = initial_method

        if description is None:
            description = f"{command_type}状态"

        # 确保配置已加载
        config = self._load_status_check_config()

        # 动态添加配置
        config[command_type] = {
            'keywords': keywords,
            'initial_method': initial_method,
            'final_method': final_method,
            'description': description
        }

        log.info(f"添加自定义状态检查配置: {command_type} -> {description}")

    def get_supported_command_types(self) -> list:
        """
        获取支持的命令类型列表

        Returns:
            list: 支持的命令类型列表
        """
        return list(self.STATUS_CHECK_CONFIG.keys())

    def get_command_keywords(self, command_type: str = None) -> dict:
        """
        获取命令关键词映射

        Args:
            command_type: 特定命令类型（可选）

        Returns:
            dict: 关键词映射字典
        """
        if command_type:
            if command_type in self.STATUS_CHECK_CONFIG:
                return {command_type: self.STATUS_CHECK_CONFIG[command_type]['keywords']}
            else:
                return {}

        return {cmd_type: config['keywords']
                for cmd_type, config in self.STATUS_CHECK_CONFIG.items()}

    def _ensure_page_ready(self, ella_app):
        """确保页面就绪"""
        assert ella_app.ensure_on_chat_page(), "无法确保在对话页面"
        assert ella_app.ensure_input_box_ready(), "输入框未就绪"

    def _execute_command(self, ella_app, command: str):
        """执行命令"""
        success = ella_app.execute_text_command(command)
        assert success, f"执行命令失败: {command}"
        log.info(f"✅ 成功执行命令: {command}")

        # 在执行命令后处理可能出现的弹窗
        self._handle_popup_after_command(ella_app, command)

    def _wait_and_get_response(self, ella_app, timeout: int = 8, max_return_attempts: int = 3):
        """
        等待并获取响应

        Args:
            ella_app: Ella应用实例
            timeout: 等待响应超时时间
            max_return_attempts: 最大返回Ella应用尝试次数

        Returns:
            list: 响应文本列表
        """
        # 等待AI响应
        response_received = ella_app.wait_for_response(timeout=timeout)

        if not response_received:
            log.warning("⚠️ 响应超时，尝试直接获取")
            time.sleep(3)

        # 确保返回到Ella应用以获取响应文本
        log.info("确保返回到Ella应用以获取响应文本")

        # 多次尝试确保回到Ella页面
        for attempt in range(max_return_attempts):
            log.info(f"第{attempt + 1}次尝试确保在Ella页面")

            # 检查是否在Ella对话页面
            if ella_app.ensure_on_chat_page():
                log.info("✅ 已确认在Ella对话页面")
                break

            # 如果不在，尝试返回
            log.warning(f"不在Ella对话页面，第{attempt + 1}次尝试返回")
            if ella_app.return_to_ella_app():
                time.sleep(2)
                # 再次检查是否成功返回
                if ella_app.ensure_on_chat_page():
                    log.info(f"✅ 第{attempt + 1}次尝试成功返回Ella页面")
                    break
                else:
                    log.warning(f"第{attempt + 1}次返回后仍不在Ella页面")
            else:
                log.error(f"第{attempt + 1}次返回Ella应用失败")
        else:
            # 所有尝试都失败
            log.error(f"经过{max_return_attempts}次尝试仍无法返回Ella页面，强制获取响应")

        # 获取响应文本
        response_text = self._safe_get_response_text(ella_app)
        log.info(f"AI响应: '{response_text}'")
        return response_text

    def _safe_get_response_text(self, ella_app) -> list:
        """
        安全获取响应文本

        Args:
            ella_app: Ella应用实例

        Returns:
            list: 响应文本列表
        """
        try:
            # 优先使用智能方法获取所有文本
            response_text = ella_app.get_response_all_text()

            # 如果获取失败或为空，尝试备用方法
            if not response_text or (isinstance(response_text, list) and not any(response_text)):
                log.warning("get_response_all_text()返回空，尝试备用方法")
                backup_text = ella_app.get_response_text()
                if backup_text:
                    response_text = [backup_text]
                else:
                    log.warning("所有响应获取方法都返回空")
                    response_text = []

            # 确保返回列表格式
            if isinstance(response_text, str):
                response_text = [response_text]
            elif not isinstance(response_text, list):
                response_text = []

            return response_text

        except Exception as e:
            log.error(f"获取响应文本异常: {e}")
            return []

    def _verify_status_change(self, initial_status, final_status, command: str):
        """验证状态变化"""
        if "bluetooth" in command.lower():
            if "open" in command.lower():
                assert final_status, f"蓝牙未开启: 初始={initial_status}, 最终={final_status}"
            elif "close" in command.lower():
                assert not final_status, f"蓝牙未关闭: 初始={initial_status}, 最终={final_status}"
        elif ("contact" in command.lower() or "contacts" in command.lower()) and "open" in command.lower():
            assert final_status, f"联系人应用未打开: 初始={initial_status}, 最终={final_status}"

        log.info(f"✅ 状态验证通过: {initial_status} -> {final_status}")

    def create_test_summary(self, command: str, initial_status, final_status, response_text: str):
        """创建测试总结"""
        status_change = "是" if initial_status != final_status else "否"

        summary = f"""
测试命令: {command}
响应内容: {response_text}
初始状态: {initial_status}
最终状态: {final_status}
状态变化: {status_change}
测试结果: 成功
"""
        return summary.strip()

    def attach_test_summary(self, summary: str):
        """附加测试总结到Allure报告"""
        allure.attach(summary, name="测试总结", attachment_type=allure.attachment_type.TEXT)

    def take_screenshot(self, ella_app, name: str):
        """截图并附加到Allure报告"""
        screenshot_path = ella_app.screenshot(f"{name}.png")
        allure.attach.file(screenshot_path, name=name, attachment_type=allure.attachment_type.PNG)
        return screenshot_path

    def verify_expected_in_response(self, expected_text, response_text):
        """
        验证期望内容是否在响应中

        Args:
            expected_text: 期望的文本内容，可以是字符串或字符串列表
            response_text: 响应文本，可以是字符串或字符串列表

        Returns:
            bool: 如果是字符串，返回是否包含；如果是列表，返回是否所有期望都包含
        """
        log.info(f"verify_expected_in_response 响应类型: {type(response_text)}, 内容: {response_text}")

        # 处理 expected_text 参数
        if isinstance(expected_text, str):
            expected_list = [expected_text]
        elif isinstance(expected_text, list):
            expected_list = expected_text
        else:
            log.error(f"❌ expected_text类型错误: {type(expected_text)}")
            return False

        # 处理 response_text 参数，统一转换为字符串进行搜索
        if isinstance(response_text, str):
            # 如果是字符串，直接使用
            search_text = response_text
            log.debug(f"响应文本(字符串): {search_text}")
        elif isinstance(response_text, list):
            # 如果是列表，过滤空值并合并为一个字符串
            filtered_texts = [text for text in response_text if text and text.strip()]
            search_text = " ".join(filtered_texts)
            log.debug(f"响应文本(列表转换): 原始列表={response_text}, 过滤后={filtered_texts}, 合并后={search_text}")
        else:
            log.error(f"❌ response_text类型错误: {type(response_text)}")
            return False

        # 如果合并后的文本为空，记录警告
        if not search_text or not search_text.strip():
            log.warning("⚠️ 响应文本为空或只包含空白字符")
            search_text = ""

        # 记录所有验证结果
        all_found = True
        found_items = []
        missing_items = []

        # 遍历所有期望内容
        for expected_item in expected_list:
            if not expected_item or not expected_item.strip():
                log.warning(f"⚠️ 跳过空的期望内容: '{expected_item}'")
                continue

            # 在合并的文本中搜索
            if expected_item.lower() in search_text.lower():
                found_items.append(expected_item)
                log.info(f"✅ 响应包含期望内容: '{expected_item}'")
            else:
                missing_items.append(expected_item)
                log.warning(f"⚠️ 响应未包含期望内容: '{expected_item}'")
                all_found = False

        # 输出总结
        if all_found:
            log.info(f"🎉 所有期望内容都已找到 ({len(found_items)}/{len(expected_list)})")
        else:
            log.warning(f"❌ 部分期望内容未找到 ({len(found_items)}/{len(expected_list)})")
            log.warning(f"缺失内容: {missing_items}")
            log.warning(f"搜索文本: '{search_text}'")

        assert all_found, f"响应未包含期望内容: {missing_items}"
        return all_found

    def verify_expected_in_response_advanced(self, expected_text, response_text,
                                             search_mode: str = "combined",
                                             match_any: bool = False):
        """
        高级版本的响应验证方法，支持多种搜索模式

        Args:
            expected_text: 期望的文本内容，可以是字符串或字符串列表
            response_text: 响应文本，可以是字符串或字符串列表
            search_mode: 搜索模式
                - "combined": 将列表合并为一个字符串搜索（默认）
                - "individual": 在列表的每个元素中分别搜索，找到即停止
                - "any_item": 与individual模式相同，在列表中任意一个元素包含即可
            match_any: 是否只要匹配任意一个期望内容即可（默认False，需要全部匹配）

        Returns:
            bool: 验证是否通过
        """
        log.info(
            f"verify_expected_in_response_advanced 响应类型: {type(response_text)}, 搜索模式: {search_mode}, 匹配模式: {'任意匹配' if match_any else '全部匹配'}")

        # 处理 expected_text 参数
        if isinstance(expected_text, str):
            expected_list = [expected_text]
        elif isinstance(expected_text, list):
            expected_list = expected_text
        else:
            log.error(f"❌ expected_text类型错误: {type(expected_text)}")
            return False

        # 处理 response_text 参数
        if isinstance(response_text, str):
            response_list = [response_text]
        elif isinstance(response_text, list):
            # 过滤空值
            response_list = [text for text in response_text if text and text.strip()]
        else:
            log.error(f"❌ response_text类型错误: {type(response_text)}")
            return False

        if not response_list:
            log.warning("⚠️ 响应文本列表为空")
            return False

        # 记录验证结果
        found_items = []
        missing_items = []

        # 根据搜索模式进行验证
        for expected_item in expected_list:
            if not expected_item or not expected_item.strip():
                log.warning(f"⚠️ 跳过空的期望内容: '{expected_item}'")
                continue

            item_found = False

            if search_mode == "combined":
                # 合并模式：将所有响应文本合并后搜索
                combined_text = " ".join(response_list)
                if expected_item.lower() in combined_text.lower():
                    item_found = True
                    log.info(f"✅ [合并模式] 找到期望内容: '{expected_item}'")

            elif search_mode in ["individual", "any_item"]:
                # 独立/任意项模式：在每个响应文本中分别搜索，找到即停止
                mode_name = "独立模式" if search_mode == "individual" else "任意项模式"
                for i, response_item in enumerate(response_list):
                    if expected_item.lower() in response_item.lower():
                        item_found = True
                        log.info(
                            f"✅ [{mode_name}] 在响应项{i + 1}中找到期望内容: '{expected_item}' -> '{response_item}'")
                        break

            else:
                log.error(f"❌ 不支持的搜索模式: {search_mode}")
                return False

            # 记录结果
            if item_found:
                found_items.append(expected_item)
                if match_any:
                    # 如果是任意匹配模式，找到一个就可以返回成功
                    log.info(f"🎉 [任意匹配模式] 找到期望内容，验证通过: '{expected_item}'")
                    return True
            else:
                missing_items.append(expected_item)
                log.warning(f"⚠️ 未找到期望内容: '{expected_item}'")

        # 输出总结
        all_found = len(missing_items) == 0
        if all_found:
            log.info(f"🎉 所有期望内容都已找到 ({len(found_items)}/{len(expected_list)})")
        else:
            log.warning(f"❌ 部分期望内容未找到 ({len(found_items)}/{len(expected_list)})")
            log.warning(f"缺失内容: {missing_items}")
            log.warning(f"响应内容: {response_list}")

        return all_found

    def manual_popup_check(self, ella_app):
        """
        手动检查并处理弹窗 - 使用最新优化的弹窗处理方法

        Args:
            ella_app: Ella应用实例

        Returns:
            bool: 是否检测到并处理了弹窗
        """
        try:
            popup_tool = self._get_popup_tool(ella_app)
            if popup_tool is None:
                log.warning("⚠️ 弹窗工具未初始化")
                return False

            # 使用优化后的单次检测方法
            success = popup_tool.detect_and_close_popup_once(debug_mode=False)

            if success:
                log.info("✅ 手动弹窗检测并处理成功")
                return True
            else:
                log.debug("ℹ️ 手动检测未发现弹窗")
                return False

        except Exception as e:
            log.warning(f"⚠️ 手动弹窗检查异常: {e}")
            return False

    def _execute_multimodal_operation(self, ella_app, multimodal_type: str) -> bool:
        """
        执行多模态操作

        Args:
            ella_app: Ella应用实例
            multimodal_type: 多模态类型

        Returns:
            bool: 是否执行成功
        """
        try:
            log.info(f"🚀 开始执行多模态操作: {multimodal_type}")

            # 确保多模态处理器存在
            if not hasattr(ella_app, 'multimodal_handler') or ella_app.multimodal_handler is None:
                log.warning("⚠️ 多模态处理器未初始化，跳过多模态执行")
                return False

            # 执行多模态功能
            success = ella_app.multimodal_handler.execute_multimodal_function(multimodal_type)

            if success:
                log.info(f"✅ 多模态功能执行成功: {multimodal_type}")
                # 等待多模态操作完成后返回对话页面
                time.sleep(2)
                ella_app.ensure_on_chat_page()
            else:
                log.warning(f"⚠️ 多模态功能执行失败: {multimodal_type}")

            return success

        except Exception as e:
            log.error(f"❌ 多模态操作执行异常: {e}")
            return False

    def _create_enhanced_test_summary(self, command: str, initial_status, final_status,
                                      response_text, multimodal_type: str = None,
                                      multimodal_executed: bool = False) -> str:
        """
        创建增强的测试总结，包含多模态信息

        Args:
            command: 测试命令
            initial_status: 初始状态
            final_status: 最终状态
            response_text: 响应文本
            multimodal_type: 多模态类型
            multimodal_executed: 是否执行了多模态功能

        Returns:
            str: 增强的测试总结
        """
        status_change = "是" if initial_status != final_status else "否"

        summary = f"""
测试命令: {command}
响应内容: {response_text}
初始状态: {initial_status}
最终状态: {final_status}
状态变化: {status_change}"""

        # 添加多模态信息
        if multimodal_type:
            summary += f"""
多模态类型: {multimodal_type}
多模态执行: {'是' if multimodal_executed else '否'}"""

        summary += """
测试结果: 成功
"""
        return summary.strip()


class SimpleEllaTest(BaseEllaTest):
    """极简版Ella测试基类"""

    def simple_command_test(self, ella_app, command: str, verify_status: bool = True,
                            verify_files: bool = False, multimodal_type: str = None):
        """
        极简命令测试方法

        Args:
            ella_app: Ella应用实例
            command: 要执行的命令
            verify_status: 是否验证状态变化
            verify_files: 是否验证文件
            multimodal_type: 多模态类型 (document/gallery/camera/ai_image_generator)，默认为None
        """
        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text, files_status = self.execute_command_and_verify(
                ella_app, command, verify_status, verify_files, multimodal_type
            )

        with allure.step("记录测试结果"):
            # 如果指定了多模态类型，创建增强的测试总结
            if multimodal_type:
                summary = self._create_enhanced_test_summary(
                    command, initial_status, final_status, response_text,
                    multimodal_type, True
                )
            else:
                summary = self.create_test_summary(command, initial_status, final_status, response_text)

            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")

        multimodal_info = f" (多模态: {multimodal_type})" if multimodal_type else ""
        log.info(f"🎉 {command} 测试完成{multimodal_info}")
        return initial_status, final_status, response_text, files_status

    def enable_popup_handling(self, timeout: int = 3, check_interval: float = 0.3):
        """
        启用弹窗处理

        Args:
            timeout: 弹窗检测超时时间（秒）
            check_interval: 弹窗检查间隔（秒）
        """
        self.set_popup_handling_config(True, timeout, check_interval)
        log.info("✅ 弹窗处理已启用（使用最新优化方法）")

    def disable_popup_handling(self):
        """禁用弹窗处理"""
        self.set_popup_handling_config(False, self._popup_timeout, self._popup_check_interval)
        log.info("⚠️ 弹窗处理已禁用")

    def quick_popup_check_and_close(self, ella_app):
        """
        快速弹窗检查和关闭（推荐方法）

        Args:
            ella_app: Ella应用实例

        Returns:
            bool: 是否检测到并处理了弹窗
        """
        try:
            popup_tool = self._get_popup_tool(ella_app)
            if popup_tool is None:
                log.warning("⚠️ 弹窗工具未初始化")
                return False

            # 使用最新的单次检测方法
            success = popup_tool.detect_and_close_popup_once(debug_mode=False)

            if success:
                log.info("✅ 快速弹窗检查和关闭成功")
            else:
                log.debug("ℹ️ 快速检查未发现弹窗")

            return success

        except Exception as e:
            log.warning(f"⚠️ 快速弹窗检查异常: {e}")
            return False


if __name__ == '__main__':
    base_test = BaseEllaTest()
    expected_text = ["Done", "蓝牙已成功开启", "设备可被发现"]
    response_text = ["蓝牙已成功开启，设备可以被发现，连接状态良好"]
    result = base_test.verify_expected_in_response_advanced(expected_text, response_text, match_any=True)
    print(result)
